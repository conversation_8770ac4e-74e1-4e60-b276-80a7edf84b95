import React, { useState, useEffect, memo } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface MoneyInputProps {
  id: string;
  name: string;
  value: number;
  label: string;
  step?: number;
  onChange: (name: string, value: number) => void;
}

const MoneyInput = memo(({ 
  id, 
  name, 
  value, 
  label, 
  step = 1,
  onChange 
}: MoneyInputProps) => {
  const [localValue, setLocalValue] = useState(value.toString());
  const [isFocused, setIsFocused] = useState(false);

  // Only update local value when prop changes AND field is not focused
  useEffect(() => {
    if (!isFocused) {
      setLocalValue(value.toString());
    }
  }, [value, isFocused]);

  const handleLocalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    
    // Only update parent if it's a valid number
    if (newValue === '' || !isNaN(parseFloat(newValue))) {
      const numValue = newValue === '' ? 0 : parseFloat(newValue);
      onChange(name, numValue);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleLocalBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    const numValue = parseFloat(localValue);
    if (isNaN(numValue)) {
      setLocalValue('0');
      onChange(name, 0);
    } else {
      setLocalValue(numValue.toString());
      onChange(name, numValue);
    }
  };

  return (
    <div>
      <Label htmlFor={id} className="text-sm font-medium block mb-2">{label}</Label>
      <div className="relative">
        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
        <Input
          id={id}
          name={name}
          type="number"
          value={localValue}
          onChange={handleLocalChange}
          onFocus={handleFocus}
          onBlur={handleLocalBlur}
          className="pl-7 h-10 w-full"
          step={step}
          min="0"
        />
      </div>
    </div>
  );
});

MoneyInput.displayName = 'MoneyInput';

export default MoneyInput;
