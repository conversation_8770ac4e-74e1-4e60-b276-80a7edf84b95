import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { formatCurrency, formatPercentage } from '@/utils/formatting';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface InputPanelProps {
  inputs: {
    initialInvestment: number;
    numRolloverCases: number;
    pacPerCase: number;
    numPacCases: number;
    projectionYears: number;
    marketGrowth: number;
    numRLs: number;
    businessOwnerAgencies: number;
    businessOwnerRLsPerAgency: number;
    branchOfficeAgencies: number;
    branchOfficeRLsPerAgency: number;
  };
  onChange: (name: string, value: number) => void;
  onCalculate: () => void;
}

const InputPanel: React.FC<InputPanelProps> = ({
  inputs,
  onChange,
  onCalculate
}) => {
  const handleSliderChange = (name: string, value: number[]) => {
    onChange(name, value[0]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Allow empty string for clearing the field
    if (value === '') {
      onChange(name, 0);
      return;
    }

    // Allow any numeric input during typing, including partial numbers
    const numValue = parseFloat(value);
    if (!isNaN(numValue) || value.endsWith('.')) {
      // Only update if it's a valid number or ends with decimal point (for typing)
      if (!isNaN(numValue)) {
        onChange(name, numValue);
      }
    }
  };

  // Handle blur events to clean up any incomplete inputs
  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      // Reset to 0 if invalid number on blur
      onChange(name, 0);
    }
  };

  // Custom slider component that changes color based on value
  const MarketGrowthSlider = () => {
    const isHighRisk = inputs.marketGrowth > 0.11;
    const sliderColor = isHighRisk ? 'bg-red-500' : 'bg-green-500';

    return (
      <div>
        <div className="flex justify-between items-center mb-2">
          <Label className="text-sm font-medium">Annual Market Growth</Label>
          <span className="text-sm font-semibold">{formatPercentage(inputs.marketGrowth)}</span>
        </div>

        <div className="mb-2">
          <SliderPrimitive.Root
            className="relative flex w-full touch-none select-none items-center"
            value={[inputs.marketGrowth * 100]}
            min={3}
            max={20}
            step={0.5}
            onValueChange={(value) => handleSliderChange('marketGrowth', [value[0] / 100])}
          >
            <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-slate-200">
              <SliderPrimitive.Range className={cn("absolute h-full", sliderColor)} />
            </SliderPrimitive.Track>
            <SliderPrimitive.Thumb
              className={cn(
                "block h-5 w-5 rounded-full border-2 bg-white shadow-md transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-110",
                isHighRisk ? "border-red-500" : "border-green-500"
              )}
            />
          </SliderPrimitive.Root>
        </div>

        <div className="flex justify-between text-xs text-gray-500">
          <span>3%</span>
          <span className={isHighRisk ? "text-red-500 font-medium" : "text-green-500 font-medium"}>
            {isHighRisk ? "High Risk" : "Conservative"}
          </span>
          <span>20%</span>
        </div>
      </div>
    );
  };

  // Custom money input component with custom step values
  const MoneyInput = ({
    id,
    name,
    value,
    label,
    step = 1
  }: {
    id: string,
    name: string,
    value: number,
    label: string,
    step?: number
  }) => {
    const [localValue, setLocalValue] = useState(value.toString());
    const [isFocused, setIsFocused] = useState(false);

    // Only update local value when prop changes AND field is not focused
    useEffect(() => {
      if (!isFocused) {
        setLocalValue(value.toString());
      }
    }, [value, isFocused]);

    const handleLocalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setLocalValue(newValue);

      // Only update parent if it's a valid number
      if (newValue === '' || !isNaN(parseFloat(newValue))) {
        const numValue = newValue === '' ? 0 : parseFloat(newValue);
        onChange(name, numValue);
      }
    };

    const handleFocus = () => {
      setIsFocused(true);
    };

    const handleLocalBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      const numValue = parseFloat(localValue);
      if (isNaN(numValue)) {
        setLocalValue('0');
        onChange(name, 0);
      } else {
        setLocalValue(numValue.toString());
        onChange(name, numValue);
      }
    };

    return (
      <div>
        <Label htmlFor={id} className="text-sm font-medium block mb-2">{label}</Label>
        <div className="relative">
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
          <Input
            id={id}
            name={name}
            type="number"
            value={localValue}
            onChange={handleLocalChange}
            onFocus={handleFocus}
            onBlur={handleLocalBlur}
            className="pl-7 h-10 w-full"
            step={step}
            min="0"
          />
        </div>
      </div>
    );
  };

  // Custom number input component
  const NumberInput = ({
    id,
    name,
    value,
    label,
    step = 1
  }: {
    id: string,
    name: string,
    value: number,
    label: string,
    step?: number
  }) => {
    const [localValue, setLocalValue] = useState(value.toString());
    const [isFocused, setIsFocused] = useState(false);

    // Only update local value when prop changes AND field is not focused
    useEffect(() => {
      if (!isFocused) {
        setLocalValue(value.toString());
      }
    }, [value, isFocused]);

    const handleLocalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setLocalValue(newValue);

      // Only update parent if it's a valid number
      if (newValue === '' || !isNaN(parseFloat(newValue))) {
        const numValue = newValue === '' ? 0 : parseFloat(newValue);
        onChange(name, numValue);
      }
    };

    const handleFocus = () => {
      setIsFocused(true);
    };

    const handleLocalBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      const numValue = parseFloat(localValue);
      if (isNaN(numValue)) {
        setLocalValue('0');
        onChange(name, 0);
      } else {
        setLocalValue(numValue.toString());
        onChange(name, numValue);
      }
    };

    return (
      <div>
        <Label htmlFor={id} className="text-sm font-medium block mb-2">{label}</Label>
        <Input
          id={id}
          name={name}
          type="number"
          value={localValue}
          onChange={handleLocalChange}
          onFocus={handleFocus}
          onBlur={handleLocalBlur}
          className="h-10 w-full"
          step={step}
          min="0"
        />
      </div>
    );
  };

  return (
    <Card className="shadow-md overflow-hidden">
      <CardHeader className="bg-gray-50 border-b py-4">
        <CardTitle className="text-xl font-bold text-center">Income Simulator Settings</CardTitle>
      </CardHeader>
      <CardContent className="p-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-8">
          {/* Left Column - Money-related fields */}
          <div className="grid gap-8">
            <MoneyInput
              id="initialInvestment"
              name="initialInvestment"
              value={inputs.initialInvestment}
              label="Initial Investment"
              step={5000}
            />

            <MoneyInput
              id="pacPerCase"
              name="pacPerCase"
              value={inputs.pacPerCase}
              label="PAC per Case"
              step={50}
            />
          </div>

          {/* Middle Column - Case numbers */}
          <div className="grid gap-8">
            <NumberInput
              id="numRolloverCases"
              name="numRolloverCases"
              value={inputs.numRolloverCases}
              label="Number of Cases"
            />

            <NumberInput
              id="numPacCases"
              name="numPacCases"
              value={inputs.numPacCases}
              label="Number of PAC Cases"
            />
          </div>

          {/* Right Column - Growth-related fields */}
          <div className="grid gap-8">
            <MarketGrowthSlider />

            <div>
              <div className="flex justify-between items-center mb-2">
                <Label className="text-sm font-medium">Years of Growth</Label>
                <span className="text-sm font-semibold">{inputs.projectionYears} {inputs.projectionYears === 1 ? 'year' : 'years'}</span>
              </div>

              <div className="mb-2">
                <SliderPrimitive.Root
                  className="relative flex w-full touch-none select-none items-center"
                  value={[inputs.projectionYears]}
                  min={1}
                  max={30}
                  step={1}
                  onValueChange={(value) => handleSliderChange('projectionYears', value)}
                >
                  <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-slate-200">
                    <SliderPrimitive.Range className="absolute h-full bg-green-500" />
                  </SliderPrimitive.Track>
                  <SliderPrimitive.Thumb className="block h-5 w-5 rounded-full border-2 border-green-500 bg-white shadow-md transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-110" />
                </SliderPrimitive.Root>
              </div>

              <div className="flex justify-between text-xs text-gray-500">
                <span>1 year</span>
                <span>30 years</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-10 flex justify-center">
          <Button
            onClick={onCalculate}
            className="w-full md:w-64 h-12 text-base font-medium"
            size="lg"
          >
            Calculate Income Scenarios
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default InputPanel;
