
import React from 'react';
import { SimpleScenarioResults, SimpleSimulationInputs } from '@/types/simpleTypes';
import SelfEmployedCard from './SelfEmployedCard';
import SelfEmployedTeamCard from './SelfEmployedTeamCard';
import BusinessOwnerCard from './BusinessOwnerCard';
import BranchOfficePassiveCardNew from './BranchOfficePassiveCardNew';
import { useColorTheme } from '@/contexts/ThemeContext';

interface ScenarioCardGridProps {
  results: SimpleScenarioResults;
  inputs: SimpleSimulationInputs;
  onInputChange: (name: string, value: number) => void;
}

const ScenarioCardGrid: React.FC<ScenarioCardGridProps> = ({ results, inputs, onInputChange }) => {
  const { colorTheme } = useColorTheme();

  return (
    <div className="grid grid-cols-1 gap-4">
      {/* Self Employed (SE Only) */}
      <SelfEmployedCard
        title="Self Employed (SE)"
        subtitle="Regional Leader (42.5%)"
        colorTheme={colorTheme}
        incomeData={{
          directEffort: results.selfEmployed.directEffort,
          recurringPac: results.selfEmployed.recurringPac,
          trailIncome: results.selfEmployed.trailIncome,
          total: results.selfEmployed.total
        }}
        simulatorInputs={{...inputs, aum: results.aum}}
      />

      {/* SE + Team of RLs */}
      <SelfEmployedTeamCard
        title="SE + Team of Licensed Reps"
        subtitle="RVP (62%) with Team of RLs"
        colorTheme={colorTheme}
        incomeData={{
          directEffort: results.selfEmployedTeam.directEffort,
          override: results.selfEmployedTeam.teamOverride,
          recurringPac: results.selfEmployedTeam.recurringPac,
          trailIncome: results.selfEmployedTeam.trailIncome,
          total: results.selfEmployedTeam.total
        }}
        numRLs={inputs.numRLs}
        onNumRLsChange={(value) => onInputChange('numRLs', value)}
        simulatorInputs={{...inputs, aum: results.aum}}
      />

      {/* BO with Agencies + RLs */}
      <BusinessOwnerCard
        title="Business Owner with Agencies"
        subtitle="RVP + Multiple Agency Offices"
        colorTheme={colorTheme}
        incomeData={{
          directEffort: results.businessOwner.personalOffice.directEffort,
          override: results.businessOwner.personalOffice.override,
          recurringPac: results.businessOwner.personalOffice.recurringPac,
          trailIncome: results.businessOwner.personalOffice.trailIncome,
          agencyOverride: results.businessOwner.agencyOverrides.overrideIncome,
          agencyRecurringPac: results.businessOwner.agencyOverrides.recurringPacIncome,
          agencyTrail: results.businessOwner.agencyOverrides.trailIncome,
          total: results.businessOwner.total
        }}
        numAgencies={inputs.businessOwnerAgencies}
        onNumAgenciesChange={(value) => onInputChange('businessOwnerAgencies', value)}
        numRLsPerAgency={inputs.businessOwnerRLsPerAgency}
        onNumRLsPerAgencyChange={(value) => onInputChange('businessOwnerRLsPerAgency', value)}
        simulatorInputs={{
          ...inputs,
          aum: results.aum,
          // Use Business Owner agency settings for this card
          numAgencies: inputs.businessOwnerAgencies,
          numRLsPerAgency: inputs.businessOwnerRLsPerAgency
        }}
      />

      {/* BO Passive (Owns Agencies, No Work) */}
      <BranchOfficePassiveCardNew
        title="Branch Office Passive"
        subtitle="Agency Network Income"
        colorTheme={colorTheme}
        incomeData={{
          directEffort: 0,
          recurringPac: 15000, // Hardcoded value to ensure it's passed correctly
          trailIncome: results.passiveOwner.trailIncome,
          agencyOverride: results.passiveOwner.agencyOverrides.overrideIncome,
          agencyRecurringPac: results.passiveOwner.agencyOverrides.recurringPacIncome,
          agencyTrail: results.passiveOwner.agencyOverrides.trailIncome,
          total: results.passiveOwner.total
        }}
        numAgencies={inputs.branchOfficeAgencies}
        onNumAgenciesChange={(value) => onInputChange('branchOfficeAgencies', value)}
        numRLsPerAgency={inputs.branchOfficeRLsPerAgency}
        onNumRLsPerAgencyChange={(value) => onInputChange('branchOfficeRLsPerAgency', value)}
        simulatorInputs={{
          ...inputs,
          aum: results.aum,
          // Use Branch Office agency settings for this card
          numAgencies: inputs.branchOfficeAgencies,
          numRLsPerAgency: inputs.branchOfficeRLsPerAgency
        }}
      />
    </div>
  );
};

export default ScenarioCardGrid;
