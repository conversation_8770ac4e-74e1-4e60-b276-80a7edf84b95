import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DirectEffortCard from './cards/DirectEffortCard';
import RecurringPacCard from './cards/RecurringPacCard';
import TrailIncomeCard from './cards/TrailIncomeCard';
import DirectEffortCardNew from './cards/DirectEffortCardNew';
import RecurringPacCardNew from './cards/RecurringPacCardNew';
import TrailIncomeCardNew from './cards/TrailIncomeCardNew';

interface ShowWorkDemoProps {
  simulatorInputs?: {
    initialInvestment: number;
    numRolloverCases: number;
    pacPerCase: number;
    numPacCases: number;
    projectionYears: number;
    marketGrowth: number;
  };
  incomeData: {
    directEffort: number;
    recurringPac: number;
    trailIncome: number;
  };
  aumData?: {
    projectedAUM: number;
  };
}

const ShowWorkDemo: React.FC<ShowWorkDemoProps> = ({
  simulatorInputs,
  incomeData,
  aumData
}) => {
  // Default values if no simulator inputs provided
  const defaultInputs = {
    initialInvestment: 25000,
    numRolloverCases: 5,
    pacPerCase: 250,
    numPacCases: 5,
    projectionYears: 10,
    marketGrowth: 0.10
  };

  const inputs = simulatorInputs || defaultInputs;
  const projectedAUM = aumData?.projectedAUM || 30722284; // Default from your example

  return (
    <div className="space-y-8">
      {/* Current Cards (Always show calculations) */}
      <Card>
        <CardHeader>
          <CardTitle>Current Cards - Always Show Calculations</CardTitle>
          <p className="text-sm text-muted-foreground">
            Current implementation shows calculation details all the time
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <DirectEffortCard
              value={incomeData.directEffort}
              personalProduction={{
                initialInvestment: inputs.initialInvestment,
                numRolloverCases: inputs.numRolloverCases
              }}
            />
            
            <RecurringPacCard
              value={incomeData.recurringPac}
              pacProduction={{
                pacPerCase: inputs.pacPerCase,
                numPacCases: inputs.numPacCases,
                projectionYears: inputs.projectionYears,
                marketGrowth: inputs.marketGrowth
              }}
            />
            
            <TrailIncomeCard
              value={incomeData.trailIncome}
              aumProjection={{
                initialInvestment: inputs.initialInvestment,
                numRolloverCases: inputs.numRolloverCases,
                pacPerCase: inputs.pacPerCase,
                numPacCases: inputs.numPacCases,
                projectionYears: inputs.projectionYears,
                marketGrowth: inputs.marketGrowth
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* New "Show our work" Cards */}
      <Card>
        <CardHeader>
          <CardTitle>New "Show our work" Cards</CardTitle>
          <p className="text-sm text-muted-foreground">
            Click "Show our work" button on any card to see detailed calculations
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <DirectEffortCardNew
              value={incomeData.directEffort}
              personalProduction={{
                initialInvestment: inputs.initialInvestment,
                numRolloverCases: inputs.numRolloverCases,
                commissionRate: 0.425,
                cdrRate: 0.055
              }}
            />
            
            <RecurringPacCardNew
              value={incomeData.recurringPac}
              pacData={{
                pacPerCase: inputs.pacPerCase,
                numPacCases: inputs.numPacCases,
                projectionYears: inputs.projectionYears,
                commissionRate: 0.425,
                cdrRate: 0.05
              }}
            />
            
            <TrailIncomeCardNew
              value={incomeData.trailIncome}
              trailData={{
                projectedAUM: projectedAUM,
                trailRate: 0.0025,
                marketGrowth: inputs.marketGrowth,
                projectionYears: inputs.projectionYears
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="space-y-2">
            <h4 className="font-semibold text-blue-900">How to use the new "Show our work" feature:</h4>
            <ul className="text-sm text-blue-800 space-y-1 list-disc list-inside">
              <li>Each card shows a clean summary view by default</li>
              <li>Click the "Show our work" button to see detailed calculations</li>
              <li>The calculation view shows step-by-step breakdown</li>
              <li>Click "Back" to return to the summary view</li>
              <li>Calculations are dynamically generated from your input values</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ShowWorkDemo;
