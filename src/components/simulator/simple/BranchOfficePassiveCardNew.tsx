import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { motion, AnimatePresence } from 'framer-motion';
import { formatCurrency } from '@/utils/formatting';
import { Building, ChevronUp, ChevronDown, Edit } from 'lucide-react';
import { cn } from '@/lib/utils';

// Import our specialized card components with "Show our work" feature
import DirectEffortCard from './cards/DirectEffortCardNew';
import RecurringPacCard from './cards/RecurringPacCardNew';
import TrailIncomeCard from './cards/TrailIncomeCardNew';
import AgencyNetworkCard from './cards/AgencyNetworkCardNew';
import SimpleCardTextEditor from './SimpleCardTextEditor';
import CalculationEditor from '../CalculationEditor';
import { useCalculations, IncomeType } from '@/hooks/useCalculations';

interface BranchOfficePassiveCardNewProps {
  title: string;
  subtitle: string;
  colorTheme: string;
  incomeData: {
    directEffort?: number;
    recurringPac?: number;
    trailIncome?: number;
    agencyOverride?: number;
    agencyRecurringPac?: number;
    agencyTrail?: number;
    total: number;
  };
  numAgencies?: number;
  onNumAgenciesChange?: (value: number) => void;
  numRLsPerAgency?: number;
  onNumRLsPerAgencyChange?: (value: number) => void;
  simulatorInputs?: any;
}

const BranchOfficePassiveCardNew: React.FC<BranchOfficePassiveCardNewProps> = ({
  title,
  subtitle,
  colorTheme,
  incomeData,
  numAgencies = 2,
  onNumAgenciesChange,
  numRLsPerAgency = 3,
  onNumRLsPerAgencyChange,
  simulatorInputs
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editingPrimaryCard, setEditingPrimaryCard] = useState<{
    type: string;
    title: string;
    subtitle: string;
  } | null>(null);
  const [editingCalculation, setEditingCalculation] = useState<{
    incomeType: IncomeType;
    isOpen: boolean;
  } | null>(null);
  const [primaryCardTitle, setPrimaryCardTitle] = useState(title);
  const [primaryCardSubtitle, setPrimaryCardSubtitle] = useState(subtitle);
  const [agenciesLabel, setAgenciesLabel] = useState('Agencies');
  const [rlsPerAgencyLabel, setRlsPerAgencyLabel] = useState('RLs/Agency');

  // Initialize calculations hook with simulator inputs
  const { calculations, updateCalculation, updateFromSimulatorInputs } = useCalculations(undefined, simulatorInputs);

  // Update calculations when simulator inputs change
  useEffect(() => {
    if (simulatorInputs) {
      updateFromSimulatorInputs(simulatorInputs);
    }
  }, [simulatorInputs, updateFromSimulatorInputs]);

  // Enhanced debug logging
  console.log('BranchOfficePassiveCardNew rendering with:', {
    title,
    subtitle,
    colorTheme,
    incomeData,
    isExpanded
  });

  return (
    <Card className="shadow-xl overflow-hidden border-0 transition-all duration-300 hover:shadow-2xl" data-component="BranchOfficePassiveCardNew">
        <CardHeader className="p-5 flex flex-row items-center justify-between bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <div className="flex items-center gap-2">
          <Building size={20} />
          <div>
            <CardTitle className="text-lg md:text-xl">{primaryCardTitle}</CardTitle>
            <p className="text-xs md:text-sm opacity-80">{primaryCardSubtitle}</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          {numAgencies !== undefined && onNumAgenciesChange && (
            <div className="flex items-center gap-2">
              <div className="text-xs text-white">{agenciesLabel}:</div>
              <Select
                value={numAgencies.toString()}
                onValueChange={(value) => onNumAgenciesChange(parseInt(value))}
              >
                <SelectTrigger className="w-20 h-8 bg-white/20 border-white/30 text-white">
                  <SelectValue placeholder="2" />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                    <SelectItem key={num} value={num.toString()}>
                      {num}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          {numRLsPerAgency !== undefined && onNumRLsPerAgencyChange && (
            <div className="flex items-center gap-2">
              <div className="text-xs text-white">{rlsPerAgencyLabel}:</div>
              <Select
                value={numRLsPerAgency.toString()}
                onValueChange={(value) => onNumRLsPerAgencyChange(parseInt(value))}
              >
                <SelectTrigger className="w-20 h-8 bg-white/20 border-white/30 text-white">
                  <SelectValue placeholder="3" />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                    <SelectItem key={num} value={num.toString()}>
                      {num}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          <button
            onClick={(e) => {
              e.stopPropagation();
              setEditingPrimaryCard({
                type: 'Primary Card',
                title: primaryCardTitle,
                subtitle: primaryCardSubtitle
              });
            }}
            className="text-white/70 hover:text-white p-2 rounded-full hover:bg-white/10 transition-colors"
            title="Edit card title and subtitle"
          >
            <Edit size={16} />
          </button>
        </div>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Total Monthly Income */}
        <div
          className="flex justify-between items-center py-3 cursor-pointer bg-slate-50 px-4 rounded-md"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <h3 className="text-sm uppercase text-muted-foreground font-semibold">TOTAL MONTHLY INCOME</h3>
          <div className="flex items-center">
            <span className="text-xl font-bold mr-2">{formatCurrency(incomeData.total)}</span>
            {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </div>
        </div>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="space-y-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">The Breakdown:</h3>
                  <span className="text-xl font-bold">{formatCurrency(incomeData.total)}</span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {/* Direct Effort Income */}
                  <DirectEffortCard
                    value={incomeData.directEffort || 0}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'directEffort',
                      isOpen: true
                    })}
                    personalProduction={simulatorInputs ? {
                      initialInvestment: 0, // Passive owner has no direct effort
                      numRolloverCases: 0,
                      commissionRate: 0,
                      cdrRate: 0
                    } : undefined}
                  />

                  {/* Recurring PAC Income */}
                  <RecurringPacCard
                    value={incomeData.recurringPac || 15000}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'recurringPac',
                      isOpen: true
                    })}
                    pacData={simulatorInputs ? {
                      pacPerCase: simulatorInputs.pacPerCase * 3, // Passive owner has 3x the PAC production
                      numPacCases: simulatorInputs.numPacCases,
                      projectionYears: simulatorInputs.projectionYears,
                      commissionRate: 0.62,
                      cdrRate: 0.05
                    } : undefined}
                  />

                  {/* Trail Income */}
                  <TrailIncomeCard
                    value={incomeData.trailIncome || 0}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'trailIncome',
                      isOpen: true
                    })}
                    trailData={simulatorInputs ? {
                      projectedAUM: simulatorInputs.aum?.passiveOwner?.projectedAUM || 0,
                      trailRate: 0.0025,
                      marketGrowth: simulatorInputs.marketGrowth,
                      projectionYears: simulatorInputs.projectionYears
                    } : undefined}
                  />

                  {/* Empty slot for layout completeness */}
                  <div className="hidden md:block"></div>
                </div>

                {/* Agency Network Income - Split into individual cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <AgencyNetworkCard
                    value={incomeData.agencyOverride || 0}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'agencyOverride',
                      isOpen: true
                    })}
                    agencyData={simulatorInputs ? {
                      numAgencies: simulatorInputs.numAgencies || 2,
                      numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                      agencyProduction: simulatorInputs.initialInvestment * 1.25 || 31250, // 25% higher for passive
                      overrideRate: 0.15, // 15% agency override
                      commissionRate: 0.62,
                      cdrRate: 0.055,
                      agencyStartYear: 5,
                      projectionYears: simulatorInputs.projectionYears
                    } : undefined}
                    incomeType="override"
                  />

                  <AgencyNetworkCard
                    value={incomeData.agencyRecurringPac || 0}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'agencyRecurringPac',
                      isOpen: true
                    })}
                    agencyData={simulatorInputs ? {
                      numAgencies: simulatorInputs.numAgencies || 2,
                      numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                      agencyProduction: simulatorInputs.pacPerCase * 1.5 || 750, // 50% higher for passive
                      overrideRate: 0.15, // 15% agency override
                      commissionRate: 0.62,
                      cdrRate: 0.05, // PAC CDR rate
                      agencyStartYear: 5,
                      projectionYears: simulatorInputs.projectionYears
                    } : undefined}
                    incomeType="pac"
                  />

                  <AgencyNetworkCard
                    value={incomeData.agencyTrail || 0}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'agencyTrail',
                      isOpen: true
                    })}
                    agencyData={simulatorInputs ? {
                      numAgencies: simulatorInputs.numAgencies || 2,
                      numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                      agencyProduction: 0, // Trail is based on AUM, not production
                      overrideRate: 0.15, // 15% agency override
                      commissionRate: 0.57, // Trail commission rate
                      cdrRate: 0.0025, // Trail rate
                      agencyStartYear: 5,
                      projectionYears: simulatorInputs.projectionYears
                    } : undefined}
                    incomeType="trail"
                  />
                </div>
              </div>

              <div className="text-center text-xs text-muted-foreground mt-4">
                Click on any card above to see calculation details
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>

      {/* Primary Card Text Editor Modal */}
      {editingPrimaryCard && (
        <SimpleCardTextEditor
          open={!!editingPrimaryCard}
          onOpenChange={(open) => !open && setEditingPrimaryCard(null)}
          title={editingPrimaryCard.title}
          subtitle={editingPrimaryCard.subtitle}
          cardType={editingPrimaryCard.type}
          dropdownLabels={{
            label1: { current: agenciesLabel, placeholder: 'First Label' },
            label2: { current: rlsPerAgencyLabel, placeholder: 'Second Label' }
          }}
          onSave={(newTitle, newSubtitle, dropdownLabels) => {
            setPrimaryCardTitle(newTitle);
            setPrimaryCardSubtitle(newSubtitle);
            if (dropdownLabels?.label1) setAgenciesLabel(dropdownLabels.label1);
            if (dropdownLabels?.label2) setRlsPerAgencyLabel(dropdownLabels.label2);
            setEditingPrimaryCard(null);
          }}
        />
      )}

      {/* Calculation Editor Modal */}
      {editingCalculation && (
        <CalculationEditor
          open={editingCalculation.isOpen}
          onOpenChange={(open) => !open && setEditingCalculation(null)}
          incomeType={editingCalculation.incomeType}
          initialVariables={calculations[editingCalculation.incomeType].variables}
          initialFormula={calculations[editingCalculation.incomeType].formula}
          initialTextProperties={calculations[editingCalculation.incomeType].textProperties}
          onSave={(variables, formula, textProperties) => {
            updateCalculation(editingCalculation.incomeType, {
              variables,
              formula,
              textProperties
            });
            setEditingCalculation(null);
          }}
        />
      )}
    </Card>
  );
};

export default BranchOfficePassiveCardNew;
