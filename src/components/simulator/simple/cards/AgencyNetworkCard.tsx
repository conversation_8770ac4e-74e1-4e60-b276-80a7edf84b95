import React from 'react';
import { Building, Edit } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';
import { cn } from '@/lib/utils';

interface AgencyProductionData {
  agencyAUM?: number;
  agencyPAC?: number;
  numAgencies?: number;
  numRLsPerAgency?: number;
  projectionYears?: number;
}

interface AgencyNetworkCardProps {
  value: number;
  override: number;
  recurringPac: number;
  trail: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  agencyProduction?: AgencyProductionData;
}

/**
 * A specialized card for Agency Network Income
 */
const AgencyNetworkCard: React.FC<AgencyNetworkCardProps> = ({
  value,
  override,
  recurringPac,
  trail,
  className,
  onEdit,
  showEditButton = false,
  agencyProduction
}) => {
  const renderAgencyProduction = () => {
    if (!agencyProduction) return null;

    const {
      agencyAUM, agencyPAC, numAgencies, numRLsPerAgency, projectionYears
    } = agencyProduction;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">Agency Production (Year {projectionYears}):</div>
        {numAgencies && numRLsPerAgency && (
          <div>Network: {numAgencies} agencies × {numRLsPerAgency} RLs each</div>
        )}
        {agencyAUM && (
          <div>Total Agency AUM: {formatCurrency(agencyAUM)}</div>
        )}
        {agencyPAC && (
          <div>Total Monthly PAC: {formatCurrency(agencyPAC)}</div>
        )}
      </div>
    );
  };

  return (
    <div className={cn(
      "border rounded-md shadow-sm p-4 border-l-4 border-red-500",
      className
    )}>
      <div className="flex flex-col">
        <div className="flex justify-between items-center mb-1">
          <h4 className="font-semibold">Agency Network Income</h4>
          {showEditButton && onEdit && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEdit();
              }}
              className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
              title="Edit calculation"
            >
              <Edit size={14} />
            </button>
          )}
        </div>
        <p className="text-xs text-muted-foreground mb-2">From your leaders' offices</p>
        <div className="text-2xl font-bold mb-2">{formatCurrency(value)}</div>

        <div className="mt-2">
          <div className="flex items-center text-red-500 mb-1">
            <Building size={16} className="mr-1" />
            <span className="text-xs">Agency Network</span>
          </div>
          <div className="grid grid-cols-3 gap-2 mt-2 text-xs text-muted-foreground">
            <div>
              <span>Override: </span>
              <span className="font-semibold text-red-500">{formatCurrency(override)}</span>
            </div>
            <div>
              <span>Recurring PAC: </span>
              <span className="font-semibold text-green-500">{formatCurrency(recurringPac)}</span>
            </div>
            <div>
              <span>Trail: </span>
              <span className="font-semibold text-purple-500">{formatCurrency(trail)}</span>
            </div>
          </div>
        </div>

        {renderAgencyProduction()}
      </div>
    </div>
  );
};

export default AgencyNetworkCard;
