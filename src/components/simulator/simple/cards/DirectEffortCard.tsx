import React from 'react';
import IncomeCard from './IncomeCard';
import { Briefcase } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface DirectEffortCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  personalProduction?: {
    initialInvestment: number;
    numRolloverCases: number;
    pacPerCase: number;
    numPacCases: number;
  };
}

/**
 * A specialized card for Direct Effort Income
 */
const DirectEffortCard: React.FC<DirectEffortCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  personalProduction
}) => {
  const renderPersonalProduction = () => {
    if (!personalProduction) return null;

    const { initialInvestment, numRolloverCases, pacPerCase, numPacCases } = personalProduction;
    const totalRollover = initialInvestment * numRolloverCases;
    const monthlyPAC = pacPerCase * numPacCases;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">This Month's Personal Production:</div>
        <div>Rollover Cases: {numRolloverCases} × {formatCurrency(initialInvestment)} = {formatCurrency(totalRollover)}</div>
        <div>PAC Cases: {numPacCases} × {formatCurrency(pacPerCase)}/mo = {formatCurrency(monthlyPAC)}/mo</div>
      </div>
    );
  };

  return (
    <IncomeCard
      title="Direct Effort Income"
      subtitle="This month's new business"
      value={value}
      icon={Briefcase}
      iconText="Personal Production"
      colorScheme="blue"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderPersonalProduction()}
    </IncomeCard>
  );
};

export default DirectEffortCard;
