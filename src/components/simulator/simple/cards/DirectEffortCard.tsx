import React from 'react';
import IncomeCard from './IncomeCard';
import { Briefcase } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface DirectEffortCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  personalProduction?: {
    initialInvestment: number;
    numRolloverCases: number;
    commissionRate?: number; // Optional commission rate override
  };
}

/**
 * A specialized card for Direct Effort Income
 */
const DirectEffortCard: React.FC<DirectEffortCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  personalProduction
}) => {
  const renderPersonalProduction = () => {
    if (!personalProduction) return null;

    const { initialInvestment, numRolloverCases, commissionRate = 0.425 } = personalProduction;
    const totalRollover = initialInvestment * numRolloverCases;
    const cdrRate = 0.055; // 5.5% CDR

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">Direct Effort Calculation:</div>
        <div>Cases: {numRolloverCases} × {formatCurrency(initialInvestment)} = {formatCurrency(totalRollover)}</div>
        <div>Commission: {formatCurrency(totalRollover)} × {(cdrRate * 100).toFixed(1)}% CDR × {(commissionRate * 100).toFixed(1)}% = {formatCurrency(totalRollover * cdrRate * commissionRate)}</div>
      </div>
    );
  };

  return (
    <IncomeCard
      title="Direct Effort Income"
      subtitle="This month's new business"
      value={value}
      icon={Briefcase}
      iconText="Personal Production"
      colorScheme="blue"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderPersonalProduction()}
    </IncomeCard>
  );
};

export default DirectEffortCard;
