import React from 'react';
import IncomeCard from './IncomeCard';
import { Repeat } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface RecurringPacCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  pacProduction?: {
    pacPerCase: number;
    numPacCases: number;
    projectionYears: number;
    marketGrowth: number;
    commissionRate?: number; // Optional commission rate override
  };
}

/**
 * A specialized card for Recurring PAC Income
 */
const RecurringPacCard: React.FC<RecurringPacCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  pacProduction
}) => {
  const renderPacProduction = () => {
    if (!pacProduction) return null;

    const { pacPerCase, numPacCases, projectionYears, commissionRate = 0.425 } = pacProduction;
    const projectionMonths = projectionYears * 12;

    // Calculate total PAC investment over the period (for commission calculation)
    // +1 because we include the current month plus all previous months
    const totalPacInvestment = numPacCases * pacPerCase * (projectionMonths + 1);

    const cdrRate = 0.055; // 5.5% CDR
    const totalCommission = totalPacInvestment * cdrRate * commissionRate;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">Recurring PAC Calculation:</div>
        <div>Monthly PACs: {numPacCases} × {formatCurrency(pacPerCase)} × {projectionMonths + 1} months</div>
        <div>Total PAC Investment: {formatCurrency(totalPacInvestment)}</div>
        <div>Commission: {formatCurrency(totalPacInvestment)} × {(cdrRate * 100).toFixed(1)}% CDR × {(commissionRate * 100).toFixed(1)}% = {formatCurrency(totalCommission)}</div>
      </div>
    );
  };

  return (
    <IncomeCard
      title="Recurring PAC Income"
      subtitle="This month's new PAC contributions"
      value={value}
      icon={Repeat}
      iconText="Monthly PAC"
      colorScheme="green"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderPacProduction()}
    </IncomeCard>
  );
};

export default RecurringPacCard;
