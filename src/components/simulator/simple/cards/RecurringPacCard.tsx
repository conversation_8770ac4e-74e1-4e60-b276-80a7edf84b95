import React from 'react';
import IncomeCard from './IncomeCard';
import { Repeat } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface RecurringPacCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  pacProduction?: {
    pacPerCase: number;
    numPacCases: number;
    projectionYears: number;
    marketGrowth: number;
  };
}

/**
 * A specialized card for Recurring PAC Income
 */
const RecurringPacCard: React.FC<RecurringPacCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  pacProduction
}) => {
  const renderPacProduction = () => {
    if (!pacProduction) return null;

    const { pacPerCase, numPacCases, projectionYears } = pacProduction;
    const projectionMonths = projectionYears * 12;

    // Calculate total PAC investment over the period (for commission calculation)
    const totalPacInvestment = numPacCases * pacPerCase * projectionMonths;

    const cdrRate = 0.055; // 5.5% CDR
    const commissionRate = 0.425; // 42.5% for Self Employed
    const totalCommission = totalPacInvestment * cdrRate * commissionRate;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">Recurring PAC Calculation:</div>
        <div>Monthly PACs: {numPacCases} × {formatCurrency(pacPerCase)} × {projectionMonths} months</div>
        <div>Total PAC Investment: {formatCurrency(totalPacInvestment)}</div>
        <div>Commission: {formatCurrency(totalPacInvestment)} × {(cdrRate * 100).toFixed(1)}% CDR × {(commissionRate * 100).toFixed(1)}% = {formatCurrency(totalCommission)}</div>
      </div>
    );
  };

  return (
    <IncomeCard
      title="Recurring PAC Income"
      subtitle="This month's new PAC contributions"
      value={value}
      icon={Repeat}
      iconText="Monthly PAC"
      colorScheme="green"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderPacProduction()}
    </IncomeCard>
  );
};

export default RecurringPacCard;
