import React from 'react';
import IncomeCard from './IncomeCard';
import { Repeat } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface RecurringPacCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  pacProduction?: {
    pacPerCase: number;
    numPacCases: number;
    projectionYears: number;
    marketGrowth: number;
  };
}

/**
 * A specialized card for Recurring PAC Income
 */
const RecurringPacCard: React.FC<RecurringPacCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  pacProduction
}) => {
  const renderPacProduction = () => {
    if (!pacProduction) return null;

    const { pacPerCase, numPacCases, projectionYears, marketGrowth } = pacProduction;
    const monthlyNewPAC = pacPerCase * numPacCases;

    // Calculate total PAC contributions over the projection period with compounding
    const monthlyGrowth = Math.pow(1 + marketGrowth, 1/12) - 1;
    const projectionMonths = projectionYears * 12;

    let totalPacContributions = 0;
    for (let month = 0; month < projectionMonths; month++) {
      // Each month we add new PAC contributions that grow for the remaining months
      const remainingMonths = projectionMonths - month;
      const futureValue = monthlyNewPAC * Math.pow(1 + monthlyGrowth, remainingMonths);
      totalPacContributions += futureValue;
    }

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">PAC Production Summary:</div>
        <div>New PACs This Month: {numPacCases} × {formatCurrency(pacPerCase)}/mo = {formatCurrency(monthlyNewPAC)}/mo</div>
        <div>Total PAC Value (Year {projectionYears}): {formatCurrency(totalPacContributions)}</div>
        <div>Annual Growth Rate: {(marketGrowth * 100).toFixed(1)}%</div>
      </div>
    );
  };

  return (
    <IncomeCard
      title="Recurring PAC Income"
      subtitle="This month's new PAC contributions"
      value={value}
      icon={Repeat}
      iconText="Monthly PAC"
      colorScheme="green"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderPacProduction()}
    </IncomeCard>
  );
};

export default RecurringPacCard;
