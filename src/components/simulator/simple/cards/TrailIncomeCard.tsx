import React from 'react';
import IncomeCard from './IncomeCard';
import { TrendingUp } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface TrailIncomeCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  aumProjection?: {
    initialInvestment: number;
    numRolloverCases: number;
    pacPerCase: number;
    numPacCases: number;
    projectionYears: number;
    marketGrowth: number;
  };
}

/**
 * A specialized card for Trail Income
 */
const TrailIncomeCard: React.FC<TrailIncomeCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  aumProjection
}) => {
  const renderAUMSummary = () => {
    if (!aumProjection) return null;

    const {
      initialInvestment,
      numRolloverCases,
      pacPerCase,
      numPacCases,
      projectionYears,
      marketGrowth
    } = aumProjection;

    // Calculate projected AUM with proper compounding
    const monthlyGrowth = Math.pow(1 + marketGrowth, 1/12) - 1;
    const projectionMonths = projectionYears * 12;

    // Monthly rollover contributions
    const monthlyRolloverContribution = initialInvestment * numRolloverCases;

    // Monthly PAC contributions
    const monthlyPacContribution = pacPerCase * numPacCases;

    // Calculate total AUM: rollover + PAC contributions with compounding
    let totalAUM = 0;

    // Add rollover contributions (lump sum each month that grows)
    for (let month = 0; month < projectionMonths; month++) {
      const remainingMonths = projectionMonths - month;
      const futureValueRollover = monthlyRolloverContribution * Math.pow(1 + monthlyGrowth, remainingMonths);
      totalAUM += futureValueRollover;
    }

    // Add PAC contributions (monthly contributions that grow)
    for (let month = 0; month < projectionMonths; month++) {
      const remainingMonths = projectionMonths - month;
      const futureValuePAC = monthlyPacContribution * Math.pow(1 + monthlyGrowth, remainingMonths);
      totalAUM += futureValuePAC;
    }

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">Personal AUM Projection:</div>
        <div>Monthly Rollovers: {numRolloverCases} × {formatCurrency(initialInvestment)} = {formatCurrency(monthlyRolloverContribution)}</div>
        <div>Monthly PACs: {numPacCases} × {formatCurrency(pacPerCase)} = {formatCurrency(monthlyPacContribution)}</div>
        <div>Projected AUM (Year {projectionYears}): {formatCurrency(totalAUM)}</div>
        <div>Annual Growth Rate: {(marketGrowth * 100).toFixed(1)}% | Trail Rate: 0.25%</div>
      </div>
    );
  };

  return (
    <IncomeCard
      title="Trail Income"
      subtitle="From personal AUM"
      value={value}
      icon={TrendingUp}
      iconText="Assets Under Management"
      colorScheme="purple"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderAUMSummary()}
    </IncomeCard>
  );
};

export default TrailIncomeCard;
