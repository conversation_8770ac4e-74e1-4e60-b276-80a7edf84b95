import React from 'react';
import IncomeCard from './IncomeCard';
import { TrendingUp } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface TrailIncomeCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  aumProjection?: {
    initialInvestment: number;
    numRolloverCases: number;
    pacPerCase: number;
    numPacCases: number;
    projectionYears: number;
    marketGrowth: number;
    commissionRate?: number; // Optional commission rate override
  };
}

/**
 * A specialized card for Trail Income
 */
const TrailIncomeCard: React.FC<TrailIncomeCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  aumProjection
}) => {
  const renderAUMSummary = () => {
    if (!aumProjection) return null;

    const {
      initialInvestment,
      numRolloverCases,
      pacPerCase,
      numPacCases,
      projectionYears,
      marketGrowth,
      commissionRate = 0.38 // Default to RL trail commission (38%)
    } = aumProjection;

    // Calculate projected AUM with continuous new investments and compounding
    const monthlyGrowth = Math.pow(1 + marketGrowth, 1/12) - 1;
    const projectionMonths = projectionYears * 12;

    // Monthly contributions
    const monthlyRolloverContribution = initialInvestment * numRolloverCases;
    const monthlyPacContribution = pacPerCase * numPacCases;

    // Calculate AUM with continuous new investments each month
    let totalAUM = 0;

    // For each month, add new investments and apply growth to entire AUM
    for (let month = 0; month < projectionMonths; month++) {
      // Add new rollover investment for this month
      totalAUM += monthlyRolloverContribution;

      // Add new PAC contribution for this month
      totalAUM += monthlyPacContribution;

      // Apply monthly growth to the entire AUM
      totalAUM = totalAUM * (1 + monthlyGrowth);
    }

    // Calculate trail income
    const trailRate = 0.0025; // 0.25% annually
    const monthlyTrailIncome = totalAUM * trailRate * commissionRate / 12;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">Trail Income Calculation:</div>
        <div>Projected AUM (Year {projectionYears}): {formatCurrency(totalAUM)}</div>
        <div>Trail: {formatCurrency(totalAUM)} × 0.25% × {(commissionRate * 100).toFixed(1)}% ÷ 12 = {formatCurrency(monthlyTrailIncome)}</div>
        <div>Growth: {(marketGrowth * 100).toFixed(1)}% annually with continuous new investments</div>
      </div>
    );
  };

  return (
    <IncomeCard
      title="Trail Income"
      subtitle="From personal AUM"
      value={value}
      icon={TrendingUp}
      iconText="Assets Under Management"
      colorScheme="purple"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderAUMSummary()}
    </IncomeCard>
  );
};

export default TrailIncomeCard;
