import React from 'react';
import IncomeCard from './IncomeCard';
import { TrendingUp } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface TrailIncomeCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  personalAUM?: number;
  projectionYears?: number;
}

/**
 * A specialized card for Trail Income
 */
const TrailIncomeCard: React.FC<TrailIncomeCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  personalAUM,
  projectionYears
}) => {
  const renderAUMSummary = () => {
    if (!personalAUM || !projectionYears) return null;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">Personal AUM Summary:</div>
        <div>Projected AUM (Year {projectionYears}): {formatCurrency(personalAUM)}</div>
        <div>Monthly Trail Rate: 0.25% annually</div>
      </div>
    );
  };

  return (
    <IncomeCard
      title="Trail Income"
      subtitle="From personal AUM"
      value={value}
      icon={TrendingUp}
      iconText="Assets Under Management"
      colorScheme="purple"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderAUMSummary()}
    </IncomeCard>
  );
};

export default TrailIncomeCard;
