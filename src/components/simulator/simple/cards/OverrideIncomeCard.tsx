import React from 'react';
import IncomeCard from './IncomeCard';
import { Users } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface TeamProductionData {
  personalAUM?: number;
  teamAUM?: number;
  agencyAUM?: number;
  personalPAC?: number;
  teamPAC?: number;
  agencyPAC?: number;
  numRLs?: number;
  numAgencies?: number;
  numRLsPerAgency?: number;
  projectionYears?: number;
  teamStartYear?: number;
}

interface OverrideIncomeCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  teamProduction?: TeamProductionData;
  cardType?: 'team' | 'business' | 'passive';
}

/**
 * A specialized card for Override Income
 */
const OverrideIncomeCard: React.FC<OverrideIncomeCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  teamProduction,
  cardType = 'team'
}) => {
  const renderTeamProduction = () => {
    if (!teamProduction) return null;

    const {
      personalAUM, teamAUM, agencyAUM,
      personalPAC, teamPAC, agencyPAC,
      numRLs, numAgencies, numRLsPerAgency,
      projectionYears, teamStartYear = 0
    } = teamProduction;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-600 space-y-1">
        <div className="font-medium text-gray-700 mb-1">Production Breakdown (Year {projectionYears}):</div>

        {/* Team Production (RLs) - Override income only shows team data */}
        {teamAUM && teamPAC && numRLs && cardType !== 'passive' && (
          <div className="mb-2">
            <div className="font-medium text-gray-600">Team ({numRLs} RLs, starts Year {teamStartYear + 1}):</div>
            <div>• AUM: {formatCurrency(teamAUM)}</div>
            <div>• Total PAC: {formatCurrency(teamPAC)}</div>
            <div>• Production Years: {Math.max(0, projectionYears - teamStartYear)} of {projectionYears}</div>
          </div>
        )}

        {/* Agency Production */}
        {agencyAUM && agencyPAC && numAgencies && numRLsPerAgency && (cardType === 'business' || cardType === 'passive') && (
          <div>
            <div className="font-medium text-gray-600">Agencies ({numAgencies} × {numRLsPerAgency} RLs each):</div>
            <div>• AUM: {formatCurrency(agencyAUM)}</div>
            <div>• Monthly PAC: {formatCurrency(agencyPAC)}</div>
          </div>
        )}
      </div>
    );
  };

  return (
    <IncomeCard
      title="Override Income"
      subtitle="From your personal team"
      value={value}
      icon={Users}
      iconText="Team Production"
      colorScheme="indigo"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    >
      {renderTeamProduction()}
    </IncomeCard>
  );
};

export default OverrideIncomeCard;
