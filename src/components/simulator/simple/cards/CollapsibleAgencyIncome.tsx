import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp, Building2 } from 'lucide-react';
import { formatCurrency } from '../../../../utils/formatting';
import { cn } from '../../../../lib/utils';
import AgencyNetworkCard from './AgencyNetworkCardNew';

interface CollapsibleAgencyIncomeProps {
  agencyOverride: number;
  agencyRecurringPac: number;
  agencyTrail: number;
  simulatorInputs: any;
  onEditAgencyOverride: () => void;
  onEditAgencyRecurringPac: () => void;
  onEditAgencyTrail: () => void;
  cardType: 'business' | 'passive';
}

const CollapsibleAgencyIncome: React.FC<CollapsibleAgencyIncomeProps> = ({
  agencyOverride,
  agencyRecurringPac,
  agencyTrail,
  simulatorInputs,
  onEditAgencyOverride,
  onEditAgencyRecurringPac,
  onEditAgencyTrail,
  cardType
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const totalAgencyIncome = agencyOverride + agencyRecurringPac + agencyTrail;

  const getAgencyProductionMultiplier = () => {
    return cardType === 'passive' ? 1.25 : 1; // 25% higher for passive
  };

  const getPacProductionMultiplier = () => {
    return cardType === 'passive' ? 1.5 : 1; // 50% higher for passive
  };

  return (
    <div className="mt-4">
      {/* Collapsed State - Single Card */}
      {!isExpanded && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className={cn(
            "border rounded-lg shadow-md overflow-hidden transition-all duration-300 bg-white",
            "border-l-4 border-red-500",
            "hover:shadow-lg hover:scale-[1.02] transform-gpu cursor-pointer"
          )}
          onClick={() => setIsExpanded(true)}
        >
          <div className="p-5">
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-semibold text-gray-900 text-base">Agency Network Income</h4>
              </div>
              <p className="text-sm text-gray-600 mb-3 leading-relaxed">Total from agency network</p>
              <div className="text-3xl font-bold mb-4 text-gray-900 tracking-tight">
                {formatCurrency(totalAgencyIncome)}
              </div>

              <div className="flex items-center justify-between mt-auto">
                <div className="flex items-center text-red-500">
                  <Building2 size={16} className="mr-1" />
                  <span className="text-xs">Agency Network</span>
                </div>

                <div className="flex items-center gap-2 text-red-500">
                  <span className="text-xs font-medium">Show breakdown</span>
                  <ChevronDown className="w-4 h-4" />
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Expanded State - Three Cards */}
      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-4"
        >
          {/* Collapse Button */}
          <div className="flex justify-between items-center p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
            <div className="flex items-center gap-3">
              <Building2 size={20} className="text-red-500" />
              <div>
                <span className="text-base font-semibold text-gray-900">Agency Network Breakdown</span>
                <div className="text-sm text-gray-600">
                  Total: <span className="font-bold text-gray-900">{formatCurrency(totalAgencyIncome)}</span>
                </div>
              </div>
            </div>
            <button
              onClick={() => setIsExpanded(false)}
              className="flex items-center gap-1 text-xs text-red-600 hover:text-red-700 transition-colors px-3 py-2 rounded-md hover:bg-red-100"
            >
              <span className="font-medium">Collapse</span>
              <ChevronUp className="w-4 h-4" />
            </button>
          </div>

          {/* Three Agency Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <AgencyNetworkCard
              value={agencyOverride}
              showEditButton={true}
              onEdit={onEditAgencyOverride}
              agencyData={simulatorInputs ? {
                numAgencies: simulatorInputs.numAgencies || 2,
                numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                agencyProduction: (simulatorInputs.initialInvestment || 25000) * getAgencyProductionMultiplier(),
                overrideRate: 0.15, // 15% agency override
                commissionRate: 0.62,
                cdrRate: 0.055,
                agencyStartYear: 5,
                projectionYears: simulatorInputs.projectionYears
              } : undefined}
              incomeType="override"
            />

            <AgencyNetworkCard
              value={agencyRecurringPac}
              showEditButton={true}
              onEdit={onEditAgencyRecurringPac}
              agencyData={simulatorInputs ? {
                numAgencies: simulatorInputs.numAgencies || 2,
                numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                agencyProduction: (simulatorInputs.pacPerCase || 500) * getPacProductionMultiplier(),
                overrideRate: 0.15, // 15% agency override
                commissionRate: 0.62,
                cdrRate: 0.05, // PAC CDR rate
                agencyStartYear: 5,
                projectionYears: simulatorInputs.projectionYears
              } : undefined}
              incomeType="pac"
            />

            <AgencyNetworkCard
              value={agencyTrail}
              showEditButton={true}
              onEdit={onEditAgencyTrail}
              agencyData={simulatorInputs ? {
                numAgencies: simulatorInputs.numAgencies || 2,
                numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                agencyProduction: 0, // Trail is based on AUM, not production
                overrideRate: 0.15, // 15% agency override
                commissionRate: 0.57, // Trail commission rate
                cdrRate: 0.0025, // Trail rate
                agencyStartYear: 5,
                projectionYears: simulatorInputs.projectionYears
              } : undefined}
              incomeType="trail"
            />
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CollapsibleAgencyIncome;
