import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { formatCurrency } from '../../../../utils/formatting';
import AgencyNetworkCard from './AgencyNetworkCardNew';

interface CollapsibleAgencyIncomeProps {
  agencyOverride: number;
  agencyRecurringPac: number;
  agencyTrail: number;
  simulatorInputs: any;
  onEditAgencyOverride: () => void;
  onEditAgencyRecurringPac: () => void;
  onEditAgencyTrail: () => void;
  cardType: 'business' | 'passive';
}

const CollapsibleAgencyIncome: React.FC<CollapsibleAgencyIncomeProps> = ({
  agencyOverride,
  agencyRecurringPac,
  agencyTrail,
  simulatorInputs,
  onEditAgencyOverride,
  onEditAgencyRecurringPac,
  onEditAgencyTrail,
  cardType
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const totalAgencyIncome = agencyOverride + agencyRecurringPac + agencyTrail;

  const getAgencyProductionMultiplier = () => {
    return cardType === 'passive' ? 1.25 : 1; // 25% higher for passive
  };

  const getPacProductionMultiplier = () => {
    return cardType === 'passive' ? 1.5 : 1; // 50% higher for passive
  };

  return (
    <div className="mt-4">
      {/* Collapsed State - Single Card */}
      {!isExpanded && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4 cursor-pointer hover:bg-red-100 transition-colors"
          onClick={() => setIsExpanded(true)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-sm font-medium text-slate-700">Agency Network</span>
              </div>
              <div className="text-2xl font-bold text-slate-900">
                {formatCurrency(totalAgencyIncome)}
              </div>
              <div className="text-xs text-slate-600 mt-1">
                Total from agency network
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-slate-500">Show breakdown</span>
              <ChevronDown className="w-4 h-4 text-slate-400" />
            </div>
          </div>
        </motion.div>
      )}

      {/* Expanded State - Three Cards */}
      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-4"
        >
          {/* Collapse Button */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-sm font-medium text-slate-700">Agency Network Breakdown</span>
              <span className="text-sm font-bold text-slate-900">
                {formatCurrency(totalAgencyIncome)}
              </span>
            </div>
            <button
              onClick={() => setIsExpanded(false)}
              className="flex items-center gap-1 text-xs text-slate-500 hover:text-slate-700 transition-colors"
            >
              <span>Collapse</span>
              <ChevronUp className="w-4 h-4" />
            </button>
          </div>

          {/* Three Agency Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <AgencyNetworkCard
              value={agencyOverride}
              showEditButton={true}
              onEdit={onEditAgencyOverride}
              agencyData={simulatorInputs ? {
                numAgencies: simulatorInputs.numAgencies || 2,
                numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                agencyProduction: (simulatorInputs.initialInvestment || 25000) * getAgencyProductionMultiplier(),
                overrideRate: 0.15, // 15% agency override
                commissionRate: 0.62,
                cdrRate: 0.055,
                agencyStartYear: 5,
                projectionYears: simulatorInputs.projectionYears
              } : undefined}
              incomeType="override"
            />

            <AgencyNetworkCard
              value={agencyRecurringPac}
              showEditButton={true}
              onEdit={onEditAgencyRecurringPac}
              agencyData={simulatorInputs ? {
                numAgencies: simulatorInputs.numAgencies || 2,
                numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                agencyProduction: (simulatorInputs.pacPerCase || 500) * getPacProductionMultiplier(),
                overrideRate: 0.15, // 15% agency override
                commissionRate: 0.62,
                cdrRate: 0.05, // PAC CDR rate
                agencyStartYear: 5,
                projectionYears: simulatorInputs.projectionYears
              } : undefined}
              incomeType="pac"
            />

            <AgencyNetworkCard
              value={agencyTrail}
              showEditButton={true}
              onEdit={onEditAgencyTrail}
              agencyData={simulatorInputs ? {
                numAgencies: simulatorInputs.numAgencies || 2,
                numRLsPerAgency: simulatorInputs.numRLsPerAgency || 3,
                agencyProduction: 0, // Trail is based on AUM, not production
                overrideRate: 0.15, // 15% agency override
                commissionRate: 0.57, // Trail commission rate
                cdrRate: 0.0025, // Trail rate
                agencyStartYear: 5,
                projectionYears: simulatorInputs.projectionYears
              } : undefined}
              incomeType="trail"
            />
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CollapsibleAgencyIncome;
