
import { useState, useEffect, useCallback, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNavigate } from 'react-router-dom';
import CompEdgeHeader from '@/components/CompEdgeHeader';
import SettingsPanel from '@/components/SettingsPanel';
import ErrorBoundary from '@/components/ErrorBoundary';
import { motion } from 'framer-motion';
import { AlertTriangle } from 'lucide-react';

// Import our new components
import InputPanel from '@/components/simulator/inputs/InputPanel';
import ScenarioCardGrid from '@/components/simulator/simple/ScenarioCardGrid';
import { LoadingCard } from '@/components/ui/loading';

// Import utility functions
import { calculateSimpleScenarios } from '@/utils/simpleCalculator';
import { validateSimulatorInputs, sanitizeInputValue } from '@/utils/validation';
import { SimpleSimulationInputs } from '@/types/simpleTypes';

const SimpleIncome = () => {
  const navigate = useNavigate();

  // Default inputs for simple income simulator
  const [inputs, setInputs] = useState<SimpleSimulationInputs>({
    initialInvestment: 25000,
    numRolloverCases: 5,
    pacPerCase: 250,
    numPacCases: 5,
    projectionYears: 10,
    marketGrowth: 0.10, // 10% annual growth
    numRLs: 3,
    // Business Owner agency settings
    businessOwnerAgencies: 2,
    businessOwnerRLsPerAgency: 3,
    // Branch Office Passive agency settings
    branchOfficeAgencies: 3,
    branchOfficeRLsPerAgency: 4,
    // AUM data will be populated from results
    trailRate: 0.0025 // 0.25% annual trail rate
  });

  // Add validation state
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Store calculated results
  const [results, setResults] = useState(null);

  // Memoize calculated results to avoid unnecessary recalculations
  const calculatedResults = useMemo(() => {
    try {
      return calculateSimpleScenarios({
        initialInvestment: inputs.initialInvestment,
        numRolloverCases: inputs.numRolloverCases,
        pacPerCase: inputs.pacPerCase,
        numPacCases: inputs.numPacCases,
        projectionYears: inputs.projectionYears,
        marketGrowth: inputs.marketGrowth,
        numRLs: inputs.numRLs,
        businessOwnerAgencies: inputs.businessOwnerAgencies,
        businessOwnerRLsPerAgency: inputs.businessOwnerRLsPerAgency,
        branchOfficeAgencies: inputs.branchOfficeAgencies,
        branchOfficeRLsPerAgency: inputs.branchOfficeRLsPerAgency
      });
    } catch (error) {
      console.error('Calculation error:', error);
      return null;
    }
  }, [inputs]);

  // Update results immediately without loading state flashing
  useEffect(() => {
    if (calculatedResults) {
      setResults(calculatedResults);
    }
  }, [calculatedResults]);

  // Manual calculate function for the button (if needed)
  const calculateScenarios = useCallback(() => {
    if (calculatedResults) {
      setResults(calculatedResults);
    }
  }, [calculatedResults]);

  // Handle input changes with minimal validation (only on blur or extreme values)
  const handleInputChange = (name: string, value: number) => {
    // Only apply basic sanitization for extreme values, allow normal typing
    let sanitizedValue = value;

    // Only sanitize if the value is clearly invalid (negative for most fields, or extremely large)
    switch (name) {
      case 'initialInvestment':
        if (value < 0) sanitizedValue = 0;
        if (value > 10000000) sanitizedValue = 10000000; // Allow larger values during typing
        break;
      case 'numRolloverCases':
      case 'numPacCases':
        if (value < 0) sanitizedValue = 0;
        if (value > 1000) sanitizedValue = 1000; // Allow larger values during typing
        break;
      case 'pacPerCase':
        if (value < 0) sanitizedValue = 0;
        if (value > 100000) sanitizedValue = 100000; // Allow larger values during typing
        break;
      case 'projectionYears':
        if (value < 1) sanitizedValue = 1;
        if (value > 100) sanitizedValue = 100; // Allow larger values during typing
        break;
      case 'marketGrowth':
        if (value < -1) sanitizedValue = -1;
        if (value > 5) sanitizedValue = 5; // Allow larger values during typing
        break;
      case 'numRLs':
      case 'businessOwnerAgencies':
      case 'businessOwnerRLsPerAgency':
      case 'branchOfficeAgencies':
      case 'branchOfficeRLsPerAgency':
        if (value < 0) sanitizedValue = 0;
        if (value > 100) sanitizedValue = 100; // Allow larger values during typing
        break;
    }

    const newInputs = { ...inputs, [name]: sanitizedValue };

    // Only validate and show errors for truly problematic values
    const validation = validateSimulatorInputs(newInputs);
    const criticalErrors = validation.errors.filter(error =>
      error.message.includes('cannot be negative') ||
      error.message.includes('must be at least')
    );
    setValidationErrors(criticalErrors.map(e => e.message));

    setInputs(newInputs);
  };

  // No need for additional useEffect since results update automatically

  return (
    <ErrorBoundary>
      <div className="h-screen bg-gray-50 flex flex-col">
        <CompEdgeHeader />
        <SettingsPanel />

        <ScrollArea className="flex-1">
          <motion.main
            className="container py-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="bg-white">
              <div className="p-6">
                <Tabs defaultValue="simple" onValueChange={(value) => {
                  if (value === "advanced") navigate("/advanced");
                }}>
                  <TabsList className="grid w-full grid-cols-2 mb-8">
                    <TabsTrigger value="simple">Simple Income Explainer</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced Calculator</TabsTrigger>
                  </TabsList>

                  <TabsContent value="simple" className="space-y-8">
                    {validationErrors.length > 0 && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2 text-red-700">
                          <AlertTriangle className="h-4 w-4" />
                          <span className="font-medium">Input Validation Errors:</span>
                        </div>
                        <ul className="mt-2 text-sm text-red-600 list-disc list-inside">
                          {validationErrors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <InputPanel
                      inputs={inputs}
                      onChange={handleInputChange}
                      onCalculate={calculateScenarios}
                    />

                    {results ? (
                      <ScenarioCardGrid
                        results={results}
                        inputs={inputs}
                        onInputChange={handleInputChange}
                      />
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {Array.from({ length: 4 }).map((_, i) => (
                          <LoadingCard key={i} title="Loading..." />
                        ))}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            </Card>
          </motion.main>
        </ScrollArea>
      </div>
    </ErrorBoundary>
  );
};

export default SimpleIncome;
