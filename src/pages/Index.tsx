
import React, { useEffect } from 'react';
import CompEdgeHeader from '@/components/CompEdgeHeader';
import SimulatorForm from '@/components/simulator/SimulatorForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
// ThemeProviders are now in App.tsx
import SettingsPanel from '@/components/SettingsPanel';
import { useColorTheme } from '@/contexts/ThemeContext';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useNavigate } from 'react-router-dom';

// Main content component
const MainContent = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50">
      <CompEdgeHeader />
      <SettingsPanel />

      <motion.main
        className="container mx-auto py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="space-y-8">
          {/* Introduction Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <Card className="bg-white shadow-lg border-0">
              <CardHeader className="pb-8">
                <div className="flex justify-center mb-8">
                  <Tabs defaultValue="advanced" onValueChange={(value) => {
                    if (value === "simple") navigate("/");
                  }}>
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="simple">Simple Income Explainer</TabsTrigger>
                      <TabsTrigger value="advanced">Advanced Calculator</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                <div className="text-center space-y-4">
                  <CardTitle className="text-4xl font-display font-bold text-gray-900">
                    Advanced Income Calculator
                  </CardTitle>
                  <CardDescription className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
                    Calculate potential commissions for mutual funds based on various inputs.
                    Adjust the sliders and options below to simulate different scenarios.
                  </CardDescription>
                </div>
              </CardHeader>
            </Card>
          </motion.div>

          {/* Secret Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-center space-y-8"
          >
            <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 shadow-lg">
              <CardContent className="py-12">
                <h2 className="text-3xl font-bold text-purple-800 mb-4">
                  I can keep a secret too 🤫
                </h2>

                {/* GIF Container */}
                <div className="bg-white rounded-lg border-2 border-purple-200 p-4 max-w-md mx-auto shadow-inner">
                  {/* Replace the src with your GIF path */}
                  <img
                    src="/gifs/secret.gif"
                    alt="Secret GIF"
                    className="w-full h-auto rounded-lg"
                    onError={(e) => {
                      // Fallback if GIF doesn't exist
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'block';
                    }}
                  />
                  {/* Fallback placeholder */}
                  <div className="text-purple-400 text-center" style={{ display: 'none' }}>
                    <div className="text-6xl mb-4">🎬</div>
                    <p className="text-lg font-medium">GIF Container</p>
                    <p className="text-sm text-purple-500 mt-2">Place your GIF at: <code className="bg-purple-100 px-2 py-1 rounded text-xs">/public/gifs/secret.gif</code></p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.main>
    </div>
  );
};

const Index = () => {
  return <MainContent />;
};

export default Index;
