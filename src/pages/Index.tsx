
import React, { useEffect } from 'react';
import CompEdgeHeader from '@/components/CompEdgeHeader';
import SimulatorForm from '@/components/simulator/SimulatorForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
// ThemeProviders are now in App.tsx
import SettingsPanel from '@/components/SettingsPanel';
import { useColorTheme } from '@/contexts/ThemeContext';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useNavigate } from 'react-router-dom';

// Main content component
const MainContent = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50">
      <CompEdgeHeader />
      <SettingsPanel />

      <motion.main
        className="container mx-auto py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="space-y-8">
          {/* Introduction Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <Card className="bg-white shadow-lg border-0">
              <CardHeader className="pb-8">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <Tabs defaultValue="advanced" onValueChange={(value) => {
                      if (value === "simple") navigate("/");
                    }}>
                      <TabsList className="grid w-full grid-cols-2 mb-8">
                        <TabsTrigger value="simple">Simple Income Explainer</TabsTrigger>
                        <TabsTrigger value="advanced">Advanced Calculator</TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </div>
                  <motion.span
                    whileHover={{ scale: 1.05 }}
                    className="text-sm px-3 py-2 rounded-full bg-emerald-100 text-emerald-800 font-medium ml-4"
                  >
                    CompEdge v1.0
                  </motion.span>
                </div>

                <div className="text-center space-y-4">
                  <CardTitle className="text-4xl font-display font-bold text-gray-900">
                    Advanced Income Calculator
                  </CardTitle>
                  <CardDescription className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
                    Calculate potential commissions for mutual funds based on various inputs.
                    Adjust the sliders and options below to simulate different scenarios.
                  </CardDescription>
                </div>
              </CardHeader>
            </Card>
          </motion.div>

          {/* Simulator Components */}
          <SimulatorForm />

          {/* Disclaimer */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-yellow-800">Important Disclaimer</h3>
                <p className="mt-2 text-sm text-yellow-700">
                  This simulator is for educational purposes only and does not represent an official
                  Primerica tool. Actual commission calculations may vary. Please consult your upline or the
                  company's official documentation for precise commission calculations.
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.main>
    </div>
  );
};

const Index = () => {
  return <MainContent />;
};

export default Index;
