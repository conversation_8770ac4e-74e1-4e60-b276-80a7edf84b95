
import { SimpleSimulationInputs, SimpleScenarioResults } from '@/types/simpleTypes';
import { trailsLogic } from '../simConfig/trailsLogic';
import { measurePerformance } from './performance';

// Constants for commission rates
const RVP_BP = 0.62; // 62% for RVP (Business Owner)
const RL_BP = 0.425; // 42.5% for RL (Self Employed)
const OVERRIDE_SPREAD = 0.195; // 19.5% override difference
const EQUITY_TRAIL_RATE = 0.0025; // 0.25% trail rate for equity funds
const TEAM_PRODUCTION_RATIO = 0.5; // Team members produce at 50% of upline

export function calculateSimpleScenarios(inputs: SimpleSimulationInputs): SimpleScenarioResults {
  return measurePerformance('calculateSimpleScenarios', () => {
  const {
    initialInvestment,
    numRolloverCases,
    pacPerCase,
    numPacCases,
    projectionYears,
    marketGrowth,
    numRLs,
    numAgencies,
    numRLsPerAgency
  } = inputs;

  // Convert annual growth to monthly
  const monthlyGrowth = Math.pow(1 + marketGrowth, 1/12) - 1;

  // Calculate months of projection
  const projectionMonths = projectionYears * 12;

  // Calculate monthly contributions
  const monthlyRolloverInvestment = initialInvestment * numRolloverCases; // New rollover investment each month
  const monthlyPacContribution = pacPerCase * numPacCases; // New PAC contribution each month

  // CDR rate (assuming 5.5% for mutual funds)
  const cdrRate = 0.055;

  // SELF EMPLOYED CALCULATIONS (42.5% commission rate)

  // 1. Direct Effort Income: numCases × initialInvestment × CDR × 42.5%
  const selfEmployedDirectIncome = monthlyRolloverInvestment * cdrRate * RL_BP;

  // 2. Recurring PAC Income: numPacCases × pacAmount × (numMonths + 1) × CDR × 42.5%
  // +1 because we include the current month plus all previous months
  const totalPacInvestmentOverPeriod = numPacCases * pacPerCase * (projectionMonths + 1);
  const selfEmployedPacIncome = totalPacInvestmentOverPeriod * cdrRate * RL_BP;

  // 3. Calculate AUM for trail income (complex calculation with continuous new investments)
  let personalAum = 0;

  // For each month, add new rollover investments and PAC contributions, then apply growth
  for (let month = 0; month < projectionMonths; month++) {
    // Add new rollover investment for this month
    personalAum += monthlyRolloverInvestment;

    // Add new PAC contribution for this month
    personalAum += monthlyPacContribution;

    // Apply monthly growth to the entire AUM
    personalAum = personalAum * (1 + monthlyGrowth);
  }

  // Calculate trail income based on final AUM
  const selfEmployedTrailIncome = personalAum * EQUITY_TRAIL_RATE * RL_BP / 12; // Monthly trail

  // SE + TEAM CALCULATIONS (RVP level - 62% commission rate)

  // Personal production at RVP level (62% commission rate)
  const rvpDirectIncome = monthlyRolloverInvestment * cdrRate * RVP_BP;
  const rvpPacIncome = (numPacCases * pacPerCase * (projectionMonths + 1)) * cdrRate * RVP_BP;
  const rvpTrailIncome = personalAum * EQUITY_TRAIL_RATE * RVP_BP / 12;

  // Team production calculations (starts at year 3, so only for remaining years)
  const teamStartYear = 3;
  const teamProductionYears = Math.max(0, projectionYears - teamStartYear);
  const teamProductionMonths = teamProductionYears * 12;

  // Team production at 50% of personal production
  const teamMemberProduction = TEAM_PRODUCTION_RATIO;
  const teamMonthlyRolloverInvestment = monthlyRolloverInvestment * teamMemberProduction * numRLs;
  const teamMonthlyPacContribution = monthlyPacContribution * teamMemberProduction * numRLs;

  // Calculate team AUM (only for the years they're producing)
  let teamAum = 0;

  if (teamProductionMonths > 0) {
    for (let month = 0; month < teamProductionMonths; month++) {
      // Add new rollover investment for this month
      teamAum += teamMonthlyRolloverInvestment;

      // Add new PAC contribution for this month
      teamAum += teamMonthlyPacContribution;

      // Apply monthly growth to the entire AUM
      teamAum = teamAum * (1 + monthlyGrowth);
    }
  }

  // Calculate override income from team (19.5% override spread)
  // Both should be MONTHLY income amounts
  const monthlyTeamRolloverOverride = teamMonthlyRolloverInvestment * cdrRate * OVERRIDE_SPREAD;
  const monthlyTeamPacOverride = teamMonthlyPacContribution * cdrRate * OVERRIDE_SPREAD;
  const teamOverride = monthlyTeamRolloverOverride + monthlyTeamPacOverride;

  // Calculate income from personal team of RLs for business owner
  const personalTeamOverride = teamOverride;

  // Calculate agency production (each agency has RVPs with their own teams)
  const agencyMonthlyRolloverInvestment = teamMonthlyRolloverInvestment * numAgencies;
  const agencyMonthlyPacContribution = teamMonthlyPacContribution * numAgencies;

  // Calculate agency AUM
  let agencyAum = 0;

  for (let month = 0; month < projectionMonths; month++) {
    // Add new rollover investment for this month
    agencyAum += agencyMonthlyRolloverInvestment;

    // Add new PAC contribution for this month
    agencyAum += agencyMonthlyPacContribution;

    // Apply monthly growth to the entire AUM
    agencyAum = agencyAum * (1 + monthlyGrowth);
  }

  // Calculate first-gen override for business owner (from agencies)
  const firstGenOverrideRate = 0.061; // 6.1% first generation override
  const agencyOverrideIncome = (agencyMonthlyRolloverInvestment * cdrRate * firstGenOverrideRate) +
                              (agencyMonthlyPacContribution * cdrRate * firstGenOverrideRate);

  // Calculate trail income from agencies
  const agencyTrailIncome = agencyAum * EQUITY_TRAIL_RATE * 0.075 / 12; // Using 7.5% trail override

  // Calculate recurring PAC income from agencies
  const agencyRecurringPacIncome = (numAgencies * numRLs * numPacCases * pacPerCase * (projectionMonths + 1)) * cdrRate * firstGenOverrideRate;

  // Build the result object
  const results: SimpleScenarioResults = {
    selfEmployed: {
      directEffort: selfEmployedDirectIncome,
      recurringPac: selfEmployedPacIncome,
      trailIncome: selfEmployedTrailIncome,
      total: selfEmployedDirectIncome + selfEmployedPacIncome + selfEmployedTrailIncome
    },
    selfEmployedTeam: {
      directEffort: rvpDirectIncome,
      override: teamOverride,
      recurringPac: rvpPacIncome,
      trailIncome: rvpTrailIncome + (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12),
      total: rvpDirectIncome + teamOverride + rvpPacIncome + rvpTrailIncome +
             (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12)
    },
    businessOwner: {
      personalOffice: {
        directEffort: rvpDirectIncome,
        override: personalTeamOverride,
        recurringPac: rvpPacIncome,
        trailIncome: rvpTrailIncome + (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12),
        total: rvpDirectIncome + personalTeamOverride + rvpPacIncome + rvpTrailIncome +
               (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12)
      },
      agencyOverrides: {
        overrideIncome: agencyOverrideIncome,
        recurringPacIncome: agencyRecurringPacIncome,
        trailIncome: agencyTrailIncome,
        total: agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome
      },
      total: rvpDirectIncome + personalTeamOverride + rvpPacIncome + rvpTrailIncome +
             (teamAum * EQUITY_TRAIL_RATE * OVERRIDE_SPREAD / 12) +
             agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome
    },
    passiveOwner: {
      // For passive owner, calculate recurring PAC income based on accumulated clients over years
      // Assume 3x the monthly PAC of a regular business owner to represent accumulated clients
      recurringPac: rvpPacIncome * 3,
      // Trail income is higher due to accumulated assets over years
      trailIncome: rvpTrailIncome * 2.5,
      agencyOverrides: {
        overrideIncome: agencyOverrideIncome * 1.25, // Slightly higher for passive owner who focuses on expansion
        recurringPacIncome: agencyRecurringPacIncome * 1.25,
        trailIncome: agencyTrailIncome * 1.25,
        total: (agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome) * 1.25
      },
      total: rvpPacIncome * 3 + rvpTrailIncome * 2.5 + (agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome) * 1.25
    },
    aum: {
      personal: personalAum,
      team: teamAum,
      agencies: agencyAum,
      total: personalAum + teamAum + agencyAum
    }
  };

  return results;
  });
}
