
import { SimpleSimulationInputs, SimpleScenarioResults } from '@/types/simpleTypes';
import { trailsLogic } from '../simConfig/trailsLogic';
import { measurePerformance } from './performance';
import { getCDRRate } from '../data/CDRBreakpoints';

// Constants for commission rates
const RVP_BP = 0.62; // 62% for RVP (Business Owner)
const RL_BP = 0.425; // 42.5% for RL (Self Employed)
const OVERRIDE_SPREAD = 0.195; // 19.5% override difference
const EQUITY_TRAIL_RATE = 0.0025; // 0.25% trail rate for equity funds
const RL_TRAIL_BP = 0.38; // 38% trail commission for RL (Self Employed)
const RVP_TRAIL_BP = 0.57; // 57% trail commission for RVP (Business Owner)
const TEAM_PRODUCTION_RATIO = 0.5; // Team members produce at 50% of upline

export function calculateSimpleScenarios(inputs: SimpleSimulationInputs): SimpleScenarioResults {
  return measurePerformance('calculateSimpleScenarios', () => {
  const {
    initialInvestment,
    numRolloverCases,
    pacPerCase,
    numPacCases,
    projectionYears,
    marketGrowth,
    numRLs,
    numAgencies,
    numRLsPerAgency
  } = inputs;

  // Convert annual growth to monthly
  const monthlyGrowth = Math.pow(1 + marketGrowth, 1/12) - 1;

  // Calculate months of projection
  const projectionMonths = projectionYears * 12;

  // Calculate monthly contributions
  const monthlyRolloverInvestment = initialInvestment * numRolloverCases; // New rollover investment each month
  const monthlyPacContribution = pacPerCase * numPacCases; // New PAC contribution each month

  // Calculate dynamic CDR rate based on total annual investment volume
  const totalMonthlyInvestment = monthlyRolloverInvestment + monthlyPacContribution;
  const annualInvestmentVolume = totalMonthlyInvestment * 12;
  const cdrRate = getCDRRate(annualInvestmentVolume);

  // SELF EMPLOYED CALCULATIONS (42.5% commission rate)

  // 1. Direct Effort Income: numCases × initialInvestment × CDR × 42.5%
  const selfEmployedDirectIncome = monthlyRolloverInvestment * cdrRate * RL_BP;

  // 2. Recurring PAC Income: numPacCases × pacAmount × (numMonths + 1) × CDR × 42.5%
  // +1 because we include the current month plus all previous months
  const totalPacInvestmentOverPeriod = numPacCases * pacPerCase * (projectionMonths + 1);
  const selfEmployedPacIncome = totalPacInvestmentOverPeriod * cdrRate * RL_BP;

  // 3. Calculate AUM for trail income (complex calculation with continuous new investments)
  let personalAum = 0;

  // For each month, add new rollover investments and PAC contributions, then apply growth
  for (let month = 0; month < projectionMonths; month++) {
    // Add new rollover investment for this month
    personalAum += monthlyRolloverInvestment;

    // Add new PAC contribution for this month
    personalAum += monthlyPacContribution;

    // Apply monthly growth to the entire AUM
    personalAum = personalAum * (1 + monthlyGrowth);
  }

  // Calculate trail income based on final AUM
  const selfEmployedTrailIncome = personalAum * EQUITY_TRAIL_RATE * RL_TRAIL_BP / 12; // Monthly trail

  // SE + TEAM CALCULATIONS (RVP level - 62% commission rate)

  // Personal production at RVP level (62% commission rate)
  const rvpDirectIncome = monthlyRolloverInvestment * cdrRate * RVP_BP;
  const rvpPacIncome = (numPacCases * pacPerCase * (projectionMonths + 1)) * cdrRate * RVP_BP;
  const rvpTrailIncome = personalAum * EQUITY_TRAIL_RATE * RVP_TRAIL_BP / 12;

  // Team production calculations (starts at year 3, so only for remaining years)
  const teamStartYear = 3;
  const teamProductionYears = Math.max(0, projectionYears - teamStartYear);
  const teamProductionMonths = teamProductionYears * 12;

  // Team production at 50% of personal production
  const teamMemberProduction = TEAM_PRODUCTION_RATIO;
  const teamMonthlyRolloverInvestment = monthlyRolloverInvestment * teamMemberProduction * numRLs;
  const teamMonthlyPacContribution = monthlyPacContribution * teamMemberProduction * numRLs;

  // Calculate team AUM (only for the years they're producing)
  let teamAum = 0;

  if (teamProductionMonths > 0) {
    for (let month = 0; month < teamProductionMonths; month++) {
      // Add new rollover investment for this month
      teamAum += teamMonthlyRolloverInvestment;

      // Add new PAC contribution for this month
      teamAum += teamMonthlyPacContribution;

      // Apply monthly growth to the entire AUM
      teamAum = teamAum * (1 + monthlyGrowth);
    }
  }

  // Calculate override income from team (19.5% override spread)
  const monthlyTeamRolloverOverride = teamMonthlyRolloverInvestment * cdrRate * OVERRIDE_SPREAD;

  // PAC override: total PAC contributions over team production period
  const totalTeamPacContributions = teamMonthlyPacContribution * (teamProductionMonths + 1);
  const teamPacOverride = totalTeamPacContributions * cdrRate * OVERRIDE_SPREAD;

  // Trail override: monthly trail income from team AUM
  // Override spread for trail = RVP_TRAIL_BP - RL_TRAIL_BP = 57% - 38% = 19%
  const trailOverrideSpread = RVP_TRAIL_BP - RL_TRAIL_BP;
  const monthlyTeamTrailOverride = teamAum * EQUITY_TRAIL_RATE * trailOverrideSpread / 12;

  const teamOverride = monthlyTeamRolloverOverride + teamPacOverride + monthlyTeamTrailOverride;



  // Calculate income from personal team of RLs for business owner
  const personalTeamOverride = teamOverride;

  // Calculate agency production (starts at year 5, so only for remaining years)
  const agencyStartYear = 4; // Agencies start producing in year 5 (index 4)
  const agencyProductionYears = Math.max(0, projectionYears - agencyStartYear);
  const agencyProductionMonths = agencyProductionYears * 12;

  // Each agency has 1 RVP (100% production) + 3 RLs (50% production each)
  // RVP production per agency: 100% of simulator settings
  const agencyRvpMonthlyRolloverInvestment = monthlyRolloverInvestment; // 100% production
  const agencyRvpMonthlyPacContribution = monthlyPacContribution; // 100% production

  // RL production per agency: 3 RLs at 50% each = 1.5x simulator settings
  const agencyRlMonthlyRolloverInvestment = monthlyRolloverInvestment * TEAM_PRODUCTION_RATIO * numRLs; // 3 RLs at 50%
  const agencyRlMonthlyPacContribution = monthlyPacContribution * TEAM_PRODUCTION_RATIO * numRLs; // 3 RLs at 50%

  // Total production per agency (RVP + RLs)
  const agencyTotalMonthlyRolloverInvestment = agencyRvpMonthlyRolloverInvestment + agencyRlMonthlyRolloverInvestment;
  const agencyTotalMonthlyPacContribution = agencyRvpMonthlyPacContribution + agencyRlMonthlyPacContribution;

  // Total for all agencies
  const agencyMonthlyRolloverInvestment = agencyTotalMonthlyRolloverInvestment * numAgencies;
  const agencyMonthlyPacContribution = agencyTotalMonthlyPacContribution * numAgencies;

  // Calculate agency AUM (only for the years they're producing)
  let agencyAum = 0;

  if (agencyProductionMonths > 0) {
    for (let month = 0; month < agencyProductionMonths; month++) {
      // Add new rollover investment for this month
      agencyAum += agencyMonthlyRolloverInvestment;

      // Add new PAC contribution for this month
      agencyAum += agencyMonthlyPacContribution;

      // Apply monthly growth to the entire AUM
      agencyAum = agencyAum * (1 + monthlyGrowth);
    }
  }

  // Calculate first-gen override for business owner (from agencies)
  // First gen override = RVP_BP - RL_BP = 62% - 42.5% = 19.5% (same as team override)
  const firstGenOverrideRate = RVP_BP - RL_BP; // 19.5% first generation override

  // Monthly agency rollover override (only from rollover cases)
  const monthlyAgencyRolloverOverride = agencyMonthlyRolloverInvestment * cdrRate * firstGenOverrideRate;

  // Total agency override income (only rollover, not PAC)
  const agencyOverrideIncome = monthlyAgencyRolloverOverride;

  // Calculate trail income from agencies using consistent trail override spread
  const agencyTrailIncome = agencyAum * EQUITY_TRAIL_RATE * trailOverrideSpread / 12;

  // Calculate recurring PAC income from agencies (separate income stream)
  // This represents the ongoing PAC income stream from agency clients over the agency production period
  const totalAgencyPacContributions = agencyMonthlyPacContribution * (agencyProductionMonths + 1);
  const agencyRecurringPacIncome = totalAgencyPacContributions * cdrRate * firstGenOverrideRate;





  // Build the result object
  const results: SimpleScenarioResults = {
    selfEmployed: {
      directEffort: selfEmployedDirectIncome,
      recurringPac: selfEmployedPacIncome,
      trailIncome: selfEmployedTrailIncome,
      total: selfEmployedDirectIncome + selfEmployedPacIncome + selfEmployedTrailIncome
    },
    selfEmployedTeam: {
      directEffort: rvpDirectIncome,
      override: teamOverride,
      teamOverride: teamOverride, // Add the teamOverride property as expected by the type
      recurringPac: rvpPacIncome,
      trailIncome: rvpTrailIncome + (teamAum * EQUITY_TRAIL_RATE * trailOverrideSpread / 12),
      total: rvpDirectIncome + teamOverride + rvpPacIncome + rvpTrailIncome +
             (teamAum * EQUITY_TRAIL_RATE * trailOverrideSpread / 12)
    },
    businessOwner: {
      personalOffice: {
        directEffort: rvpDirectIncome,
        override: personalTeamOverride,
        recurringPac: rvpPacIncome,
        trailIncome: rvpTrailIncome + (teamAum * EQUITY_TRAIL_RATE * trailOverrideSpread / 12),
        total: rvpDirectIncome + personalTeamOverride + rvpPacIncome + rvpTrailIncome +
               (teamAum * EQUITY_TRAIL_RATE * trailOverrideSpread / 12)
      },
      agencyOverrides: {
        overrideIncome: agencyOverrideIncome,
        recurringPacIncome: agencyRecurringPacIncome,
        trailIncome: agencyTrailIncome,
        total: agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome
      },
      total: rvpDirectIncome + personalTeamOverride + rvpPacIncome + rvpTrailIncome +
             (teamAum * EQUITY_TRAIL_RATE * trailOverrideSpread / 12) +
             agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome
    },
    passiveOwner: {
      // Use direct calculations from income simulator settings, not multipliers
      recurringPac: rvpPacIncome,
      // Trail income from accumulated personal AUM
      trailIncome: rvpTrailIncome,
      agencyOverrides: {
        overrideIncome: agencyOverrideIncome, // Direct calculation, no multiplier
        recurringPacIncome: agencyRecurringPacIncome,
        trailIncome: agencyTrailIncome,
        total: agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome
      },
      total: rvpPacIncome + rvpTrailIncome + agencyOverrideIncome + agencyRecurringPacIncome + agencyTrailIncome
    },
    aum: {
      personal: personalAum,
      team: teamAum,
      agencies: agencyAum,
      total: personalAum + teamAum + agencyAum
    }
  };



  return results;
  });
}
